<?php
/**
 * ملف معالجة إجراءات الملف الشخصي للإدمن
 */

header('Content-Type: application/json');
require_once '../../includes/init.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_profile':
            updateAdminProfile();
            break;
        case 'get_profile':
            getAdminProfile();
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
}

function updateAdminProfile() {
    global $db;
    
    try {
        $name = trim($_POST['name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        
        // التحقق من صحة البيانات
        if (empty($name)) {
            echo json_encode(['success' => false, 'message' => 'الاسم مطلوب']);
            return;
        }
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            echo json_encode(['success' => false, 'message' => 'البريد الإلكتروني غير صحيح']);
            return;
        }
        
        // معالجة رفع الصورة
        $profile_image = null;
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $profile_image = handleImageUpload($_FILES['profile_image']);
            if (!$profile_image) {
                echo json_encode(['success' => false, 'message' => 'فشل في رفع الصورة']);
                return;
            }
        }
        
        // التحقق من وجود جدول admin_profile
        createAdminProfileTableIfNotExists();
        
        // التحقق من وجود سجل للإدمن
        $db->query("SELECT id FROM admin_profile WHERE admin_id = 1");
        $existing_profile = $db->single();
        
        if ($existing_profile) {
            // تحديث السجل الموجود
            if ($profile_image) {
                $db->query("UPDATE admin_profile SET name = :name, email = :email, profile_image = :profile_image, updated_at = NOW() WHERE admin_id = 1");
                $db->bind(':profile_image', $profile_image);
            } else {
                $db->query("UPDATE admin_profile SET name = :name, email = :email, updated_at = NOW() WHERE admin_id = 1");
            }
        } else {
            // إنشاء سجل جديد
            $db->query("INSERT INTO admin_profile (admin_id, name, email, profile_image, created_at, updated_at) VALUES (1, :name, :email, :profile_image, NOW(), NOW())");
            $db->bind(':profile_image', $profile_image ?: 'assets/12.jpg');
        }
        
        $db->bind(':name', $name);
        $db->bind(':email', $email);
        
        if ($db->execute()) {
            echo json_encode([
                'success' => true, 
                'message' => 'تم تحديث الملف الشخصي بنجاح',
                'profile_image' => $profile_image
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في حفظ البيانات']);
        }
        
    } catch (Exception $e) {
        error_log("خطأ في تحديث الملف الشخصي: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
    }
}

function getAdminProfile() {
    global $db;
    
    try {
        $db->query("SELECT * FROM admin_profile WHERE admin_id = 1");
        $profile = $db->single();
        
        if ($profile) {
            echo json_encode([
                'success' => true,
                'profile' => $profile
            ]);
        } else {
            // إرجاع البيانات الافتراضية
            echo json_encode([
                'success' => true,
                'profile' => [
                    'name' => 'المدير',
                    'email' => '<EMAIL>',
                    'profile_image' => 'assets/12.jpg'
                ]
            ]);
        }
    } catch (Exception $e) {
        error_log("خطأ في جلب الملف الشخصي: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
    }
}

function handleImageUpload($file) {
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    $max_size = 5 * 1024 * 1024; // 5MB
    
    // التحقق من نوع الملف
    if (!in_array($file['type'], $allowed_types)) {
        return false;
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > $max_size) {
        return false;
    }
    
    // إنشاء مجلد uploads إذا لم يكن موجوداً
    $upload_dir = '../uploads/profile/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // إنشاء اسم ملف فريد
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $new_filename = 'admin_profile_' . time() . '.' . $file_extension;
    $upload_path = $upload_dir . $new_filename;
    
    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return 'uploads/profile/' . $new_filename;
    }
    
    return false;
}

function createAdminProfileTableIfNotExists() {
    global $db;
    
    try {
        $db->query("
            CREATE TABLE IF NOT EXISTS admin_profile (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL DEFAULT 1,
                name VARCHAR(255) NOT NULL DEFAULT 'المدير',
                email VARCHAR(255) NOT NULL DEFAULT '<EMAIL>',
                profile_image VARCHAR(500) DEFAULT 'assets/12.jpg',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_admin (admin_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $db->execute();
    } catch (Exception $e) {
        error_log("خطأ في إنشاء جدول admin_profile: " . $e->getMessage());
    }
}
?>
