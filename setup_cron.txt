# إعداد Cron Job لتنظيف العناصر المنتهية الصلاحية

## 1. تشغيل كل ساعة
# لتشغيل التنظيف كل ساعة، أضف السطر التالي إلى crontab:
0 * * * * /usr/bin/php /path/to/your/project/cleanup_expired_items.php

## 2. تشغيل كل 6 ساعات
# لتشغيل التنظيف كل 6 ساعات، أضف السطر التالي إلى crontab:
0 */6 * * * /usr/bin/php /path/to/your/project/cleanup_expired_items.php

## 3. تشغيل يومياً في الساعة 2:00 صباحاً
# لتشغيل التنظيف يومياً في الساعة 2:00 صباحاً، أضف السطر التالي إلى crontab:
0 2 * * * /usr/bin/php /path/to/your/project/cleanup_expired_items.php

## كيفية إضافة Cron Job:

### على Linux/Unix:
1. افتح terminal
2. اكتب: crontab -e
3. أضف السطر المناسب من الأعلى
4. احفظ واخرج

### على Windows (باستخدام Task Scheduler):
1. افتح Task Scheduler
2. انقر على "Create Basic Task"
3. اختر التوقيت المناسب
4. في Action، اختر "Start a program"
5. في Program/script، ضع مسار PHP: C:\xampp\php\php.exe
6. في Arguments، ضع: C:\xampp\htdocs\new1\cleanup_expired_items.php

### على cPanel:
1. اذهب إلى Cron Jobs في cPanel
2. أضف cron job جديد
3. اختر التوقيت المناسب
4. في Command، ضع: /usr/bin/php /home/<USER>/public_html/cleanup_expired_items.php

## ملاحظات مهمة:

1. تأكد من تغيير "/path/to/your/project/" إلى المسار الفعلي لمشروعك
2. تأكد من أن مسار PHP صحيح (/usr/bin/php قد يختلف حسب النظام)
3. تأكد من أن مجلد "logs" قابل للكتابة
4. يمكنك تشغيل الملف يدوياً عبر: http://yoursite.com/cleanup_expired_items.php?manual_run=1

## اختبار Cron Job:

لاختبار أن cron job يعمل بشكل صحيح:
1. شغل الملف يدوياً أولاً للتأكد من عمله
2. تحقق من ملفات السجل في مجلد logs/
3. تحقق من قاعدة البيانات للتأكد من تحديث الحالات

## مراقبة السجلات:

يتم حفظ سجلات التنظيف في:
- logs/cleanup_YYYY-MM-DD.log

يمكنك مراقبة السجلات باستخدام:
tail -f logs/cleanup_$(date +%Y-%m-%d).log

## استكشاف الأخطاء:

إذا لم يعمل cron job:
1. تحقق من مسار PHP: which php
2. تحقق من صلاحيات الملف: chmod +x cleanup_expired_items.php
3. تحقق من سجل cron: /var/log/cron (على Linux)
4. جرب تشغيل الأمر يدوياً من terminal أولاً

## أمان إضافي:

لحماية إضافية، يمكنك:
1. إضافة IP whitelist في بداية الملف
2. استخدام token أمان بدلاً من manual_run=1
3. تشفير معاملات الاتصال بقاعدة البيانات

## مثال على إعداد متقدم:

# تشغيل التنظيف كل ساعة مع إعادة توجيه الأخطاء
0 * * * * /usr/bin/php /path/to/your/project/cleanup_expired_items.php >> /path/to/your/project/logs/cron.log 2>&1

# تشغيل التنظيف يومياً مع إرسال بريد إلكتروني في حالة الخطأ
0 2 * * * /usr/bin/php /path/to/your/project/cleanup_expired_items.php || echo "Cleanup failed" | mail -s "Cleanup Error" <EMAIL>
