<?php
/**
 * اختبار حفظ السائقين
 */

require_once '../includes/init.php';

$db = new Database();

echo "<h2>اختبار حفظ السائقين</h2>";

// محاكاة البيانات المرسلة من النموذج
$test_data = [
    'action' => 'add',
    'name' => 'سائق تجريبي ' . time(),
    'phone' => '**********',
    'license_number' => 'TEST' . time(),
    'address' => 'عنوان تجريبي',
    'status' => 'available',
    'experience_years' => '5',
    'is_active' => '1',
    'license_type' => 'رخصة خاصة',
    'license_expiry' => '2025-12-31',
    'governorate' => 'الرياض',
    'city' => 'الرياض',
    'region' => 'الوسط'
];

echo "<h3>البيانات التجريبية:</h3>";
echo "<pre>" . print_r($test_data, true) . "</pre>";

try {
    // محاكاة الطلب POST
    $_POST = $test_data;
    
    // تشغيل نفس الكود الموجود في drivers_actions.php
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add') {
        // جلب البيانات من النموذج
        $name = trim($_POST['name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $license_number = trim($_POST['license_number'] ?? '');
        $address = trim($_POST['address'] ?? '');
        $status = trim($_POST['status'] ?? 'available');
        $experience_years = (int)($_POST['experience_years'] ?? 0);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        // الحقول الإضافية
        $license_type = trim($_POST['license_type'] ?? 'رخصة خاصة');
        $license_expiry = trim($_POST['license_expiry'] ?? '');
        $governorate = trim($_POST['governorate'] ?? '');
        $city = trim($_POST['city'] ?? '');
        $region = trim($_POST['region'] ?? '');

        echo "<h3>البيانات المعالجة:</h3>";
        echo "الاسم: $name<br>";
        echo "الهاتف: $phone<br>";
        echo "رقم الرخصة: $license_number<br>";
        echo "العنوان: $address<br>";
        echo "الحالة: $status<br>";
        echo "سنوات الخبرة: $experience_years<br>";
        echo "نشط: $is_active<br>";
        echo "نوع الرخصة: $license_type<br>";
        echo "انتهاء الرخصة: $license_expiry<br>";
        echo "المحافظة: $governorate<br>";
        echo "المدينة: $city<br>";
        echo "المنطقة: $region<br>";

        // التحقق من البيانات المطلوبة
        if (empty($name)) {
            throw new Exception('اسم السائق مطلوب');
        }
        if (empty($phone)) {
            throw new Exception('رقم الهاتف مطلوب');
        }
        if (empty($license_number)) {
            throw new Exception('رقم الرخصة مطلوب');
        }

        echo "<h3>✅ التحقق من البيانات نجح</h3>";

        // التحقق من عدم تكرار رقم الرخصة
        $db->query("SELECT id FROM transport_drivers WHERE license_number = :license_number");
        $db->bind(':license_number', $license_number);
        $existing = $db->single();

        if ($existing) {
            throw new Exception('رقم الرخصة موجود بالفعل');
        }

        echo "<h3>✅ التحقق من تكرار الرخصة نجح</h3>";

        // إدراج السائق الجديد
        $sql = "
            INSERT INTO transport_drivers (
                name, phone, license_number, address, status, experience_years,
                is_active, license_type, license_expiry, governorate, city, region,
                rating, created_at
            ) VALUES (
                :name, :phone, :license_number, :address, :status, :experience_years,
                :is_active, :license_type, :license_expiry, :governorate, :city, :region,
                5.0, NOW()
            )
        ";
        
        echo "<h3>استعلام SQL:</h3>";
        echo "<pre>$sql</pre>";
        
        $db->query($sql);
        
        $db->bind(':name', $name);
        $db->bind(':phone', $phone);
        $db->bind(':license_number', $license_number);
        $db->bind(':address', $address);
        $db->bind(':status', $status);
        $db->bind(':experience_years', $experience_years);
        $db->bind(':is_active', $is_active);
        $db->bind(':license_type', $license_type);
        $db->bind(':license_expiry', $license_expiry ?: null);
        $db->bind(':governorate', $governorate);
        $db->bind(':city', $city);
        $db->bind(':region', $region);

        if ($db->execute()) {
            $new_id = $db->lastInsertId();
            echo "<h3 style='color: green;'>✅ تم إضافة السائق بنجاح! (ID: $new_id)</h3>";
            
            // عرض البيانات المحفوظة
            $db->query("SELECT * FROM transport_drivers WHERE id = :id");
            $db->bind(':id', $new_id);
            $saved_driver = $db->single();
            
            echo "<h3>البيانات المحفوظة:</h3>";
            echo "<pre>" . print_r($saved_driver, true) . "</pre>";
            
            // حذف البيانات التجريبية
            $db->query("DELETE FROM transport_drivers WHERE id = :id");
            $db->bind(':id', $new_id);
            $db->execute();
            echo "<h3>✅ تم حذف البيانات التجريبية</h3>";
            
        } else {
            throw new Exception('فشل في إضافة السائق');
        }
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
    echo "<p>تفاصيل الخطأ:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<p><a href='dashboard.php' style='background: #6366f1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة للوحة التحكم</a></p>";
echo "<p><a href='check_drivers_table.php' style='background: #f59e0b; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فحص جدول السائقين</a></p>";
?>
