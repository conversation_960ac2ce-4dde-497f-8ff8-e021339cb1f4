# نظام إدارة انتهاء صلاحية الفعاليات والمواصلات والتذاكر

## نظرة عامة

تم تطوير نظام شامل لإدارة انتهاء صلاحية الفعاليات والمواصلات والتذاكر تلقائياً. يقوم النظام بالمهام التالية:

1. **إخفاء الفعاليات المنتهية** من الصفحة الرئيسية وصفحة الفعاليات
2. **تحديث حالة الرحلات** المرتبطة بالفعاليات المنتهية
3. **تحديث حالة التذاكر** المرتبطة بالفعاليات المنتهية
4. **تحديث حالة حجوزات المواصلات** المرتبطة بالفعاليات المنتهية
5. **عرض التذاكر والحجوزات المنتهية** في قسم منفصل في صفحة "تذاكري"

## الملفات المضافة/المحدثة

### الملفات الجديدة:
- `cleanup_expired_items.php` - ملف التنظيف التلقائي
- `update_database_schema.php` - ملف تحديث قاعدة البيانات
- `setup_cron.txt` - تعليمات إعداد cron job
- `EXPIRY_SYSTEM_README.md` - هذا الملف

### الملفات المحدثة:
- `includes/functions.php` - إضافة دوال إدارة انتهاء الصلاحية
- `my-tickets.php` - إضافة عرض التذاكر المنتهية
- `index.php` - تشغيل التنظيف قبل عرض الفعاليات
- `events.php` - تشغيل التنظيف قبل عرض الفعاليات
- `transport/dashboard.php` - إضافة زر التنظيف اليدوي

## خطوات التثبيت

### 1. تحديث قاعدة البيانات
```
http://yoursite.com/update_database_schema.php
```

### 2. إعداد Cron Job
راجع ملف `setup_cron.txt` للتعليمات المفصلة.

**مثال سريع:**
```bash
# تشغيل كل ساعة
0 * * * * /usr/bin/php /path/to/your/project/cleanup_expired_items.php

# تشغيل يومياً في الساعة 2:00 صباحاً
0 2 * * * /usr/bin/php /path/to/your/project/cleanup_expired_items.php
```

### 3. اختبار النظام
```
http://yoursite.com/cleanup_expired_items.php?manual_run=1
```

## كيفية عمل النظام

### 1. تحديث الفعاليات المنتهية
```sql
UPDATE events SET status = 'expired' WHERE date_time < NOW() AND status = 'active'
```

### 2. تحديث الرحلات المرتبطة
```sql
UPDATE transport_trips tt 
JOIN events e ON tt.event_id = e.id 
SET tt.is_active = 0 
WHERE e.status = 'expired' AND tt.is_active = 1
```

### 3. تحديث التذاكر المرتبطة
```sql
UPDATE tickets t 
JOIN events e ON t.event_id = e.id 
SET t.status = 'expired' 
WHERE e.status = 'expired' AND t.status = 'active'
```

### 4. تحديث حجوزات المواصلات
```sql
UPDATE transport_bookings tb 
JOIN events e ON tb.event_id = e.id 
SET tb.status = 'expired' 
WHERE e.status = 'expired' AND tb.status IN ('pending', 'confirmed')
```

## الميزات الجديدة

### 1. في الصفحة الرئيسية (index.php)
- إخفاء الفعاليات المنتهية تلقائياً
- عرض الفعاليات النشطة فقط

### 2. في صفحة الفعاليات (events.php)
- إخفاء الفعاليات المنتهية
- عرض الفعاليات المستقبلية فقط

### 3. في صفحة تذاكري (my-tickets.php)
- عرض التذاكر النشطة في القسم الرئيسي
- عرض التذاكر المنتهية في قسم منفصل
- تمييز حالة التذاكر بألوان مختلفة
- عرض حجوزات المواصلات المنتهية

### 4. في لوحة تحكم المواصلات
- زر "تنظيف يدوي" لتشغيل التنظيف فوراً
- إشعارات للمدراء عند حدوث تنظيف

## السجلات والمراقبة

### ملفات السجل
- `logs/cleanup_YYYY-MM-DD.log` - سجل يومي للتنظيف
- جدول `cleanup_logs` في قاعدة البيانات
- جدول `admin_activity_log` لتسجيل العمليات

### مراقبة السجلات
```bash
# مراقبة السجل الحالي
tail -f logs/cleanup_$(date +%Y-%m-%d).log

# عرض آخر 50 سطر
tail -50 logs/cleanup_$(date +%Y-%m-%d).log
```

## استكشاف الأخطاء

### المشاكل الشائعة:

1. **Cron job لا يعمل:**
   - تحقق من مسار PHP: `which php`
   - تحقق من صلاحيات الملف: `chmod +x cleanup_expired_items.php`
   - تحقق من سجل cron: `/var/log/cron`

2. **خطأ في قاعدة البيانات:**
   - تأكد من تشغيل `update_database_schema.php` أولاً
   - تحقق من صلاحيات قاعدة البيانات

3. **لا تظهر التذاكر المنتهية:**
   - تأكد من تشغيل التنظيف مرة واحدة على الأقل
   - تحقق من وجود فعاليات منتهية في قاعدة البيانات

## الأمان

### حماية ملف التنظيف:
- يتطلب تسجيل دخول المدير للتشغيل اليدوي
- يعمل فقط من سطر الأوامر أو بمعامل خاص
- تسجيل جميع العمليات في السجلات

### حماية إضافية:
```php
// إضافة IP whitelist
$allowed_ips = ['127.0.0.1', 'your-server-ip'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips)) {
    die('Access denied');
}
```

## الصيانة

### مهام دورية:
1. **تنظيف ملفات السجل القديمة:**
   ```bash
   find logs/ -name "cleanup_*.log" -mtime +30 -delete
   ```

2. **تنظيف جدول cleanup_logs:**
   ```sql
   DELETE FROM cleanup_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 MONTH);
   ```

3. **مراقبة أداء النظام:**
   - تحقق من أوقات التنفيذ في السجلات
   - راقب استخدام قاعدة البيانات

## التطوير المستقبلي

### ميزات مقترحة:
1. إشعارات بريد إلكتروني للمدراء
2. تقارير إحصائية شهرية
3. أرشفة البيانات المنتهية
4. استرداد الفعاليات المحذوفة خطأً
5. تنبيهات قبل انتهاء الفعاليات

### تحسينات الأداء:
1. فهرسة أعمدة التاريخ
2. تقسيم العمليات الكبيرة
3. استخدام transactions للعمليات المتعددة

## الدعم

للحصول على المساعدة:
1. راجع ملفات السجل أولاً
2. تحقق من قاعدة البيانات
3. جرب التشغيل اليدوي للتشخيص
4. تواصل مع فريق التطوير مع تفاصيل الخطأ
