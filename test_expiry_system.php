<?php
/**
 * ملف اختبار نظام انتهاء الصلاحية
 * يقوم بإنشاء بيانات تجريبية لاختبار النظام
 */

require_once 'includes/init.php';
require_once 'includes/functions.php';

// التحقق من صلاحيات المدير
session_start();
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin'])) {
    die('غير مسموح بالوصول');
}

$db = new Database();
$results = [];

try {
    // إنشاء فعالية منتهية للاختبار
    $past_date = date('Y-m-d H:i:s', strtotime('-2 days'));
    $db->query("
        INSERT INTO events (title, description, location, date_time, end_time, price, capacity, status, created_at) 
        VALUES ('فعالية اختبار منتهية', 'فعالية للاختبار انتهت منذ يومين', 'مكان الاختبار', :past_date, :past_date, 25.00, 100, 'active', NOW())
    ");
    $db->bind(':past_date', $past_date);
    $db->execute();
    $test_event_id = $db->lastInsertId();
    $results[] = "تم إنشاء فعالية اختبار منتهية (ID: $test_event_id)";
    
    // إنشاء تذكرة للفعالية المنتهية
    if (isset($_SESSION['user_id'])) {
        // إنشاء طلب
        $db->query("
            INSERT INTO orders (user_id, event_id, quantity, total_amount, payment_status, created_at) 
            VALUES (:user_id, :event_id, 1, 25.00, 'completed', NOW())
        ");
        $db->bind(':user_id', $_SESSION['user_id']);
        $db->bind(':event_id', $test_event_id);
        $db->execute();
        $test_order_id = $db->lastInsertId();
        
        // إنشاء تذكرة
        $ticket_code = generate_ticket_code();
        $db->query("
            INSERT INTO tickets (order_id, event_id, user_id, ticket_code, status, created_at) 
            VALUES (:order_id, :event_id, :user_id, :ticket_code, 'active', NOW())
        ");
        $db->bind(':order_id', $test_order_id);
        $db->bind(':event_id', $test_event_id);
        $db->bind(':user_id', $_SESSION['user_id']);
        $db->bind(':ticket_code', $ticket_code);
        $db->execute();
        $results[] = "تم إنشاء تذكرة اختبار (كود: $ticket_code)";
    }
    
    // إنشاء رحلة منتهية للاختبار
    $db->query("SHOW TABLES LIKE 'transport_trips'");
    $trips_table = $db->single();
    
    if ($trips_table) {
        // التحقق من وجود نقطة انطلاق
        $db->query("SELECT id FROM transport_starting_points LIMIT 1");
        $starting_point = $db->single();
        
        if ($starting_point) {
            $past_departure = date('Y-m-d H:i:s', strtotime('-1 day'));
            $db->query("
                INSERT INTO transport_trips (event_id, starting_point_id, departure_time, arrival_time, price, total_seats, available_seats, is_active, created_at) 
                VALUES (:event_id, :starting_point_id, :departure_time, :arrival_time, 30.00, 50, 50, 1, NOW())
            ");
            $db->bind(':event_id', $test_event_id);
            $db->bind(':starting_point_id', $starting_point['id']);
            $db->bind(':departure_time', $past_departure);
            $db->bind(':arrival_time', date('Y-m-d H:i:s', strtotime($past_departure . ' +2 hours')));
            $db->execute();
            $test_trip_id = $db->lastInsertId();
            $results[] = "تم إنشاء رحلة اختبار منتهية (ID: $test_trip_id)";
            
            // إنشاء حجز مواصلات للرحلة المنتهية
            if (isset($_SESSION['user_id'])) {
                $booking_code = 'TEST' . strtoupper(substr(md5(time()), 0, 6));
                $db->query("
                    INSERT INTO transport_bookings (user_id, trip_id, event_id, customer_name, customer_phone, passenger_name, passenger_phone, passengers_count, total_amount, payment_method, booking_code, status, created_at) 
                    VALUES (:user_id, :trip_id, :event_id, 'مستخدم اختبار', '0599123456', 'مستخدم اختبار', '0599123456', 1, 30.00, 'credit_card', :booking_code, 'confirmed', NOW())
                ");
                $db->bind(':user_id', $_SESSION['user_id']);
                $db->bind(':trip_id', $test_trip_id);
                $db->bind(':event_id', $test_event_id);
                $db->bind(':booking_code', $booking_code);
                $db->execute();
                $results[] = "تم إنشاء حجز مواصلات اختبار (كود: $booking_code)";
            }
        }
    }
    
    $results[] = "تم إنشاء البيانات التجريبية بنجاح";
    
} catch (Exception $e) {
    $results[] = "خطأ في إنشاء البيانات التجريبية: " . $e->getMessage();
}

// تشغيل نظام التنظيف
$cleanup_results = run_expiry_cleanup();

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام انتهاء الصلاحية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">
                <i class="fas fa-vial text-green-600 ml-2"></i>
                اختبار نظام انتهاء الصلاحية
            </h1>
            
            <!-- نتائج إنشاء البيانات التجريبية -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-2">
                    <i class="fas fa-database ml-2"></i>
                    إنشاء البيانات التجريبية
                </h2>
                <ul class="list-disc list-inside text-blue-700">
                    <?php foreach ($results as $result): ?>
                    <li><?php echo htmlspecialchars($result); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            
            <!-- نتائج التنظيف -->
            <?php if ($cleanup_results['success']): ?>
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <h2 class="text-lg font-semibold text-green-800 mb-2">
                    <i class="fas fa-check-circle ml-2"></i>
                    نتائج التنظيف
                </h2>
                <p class="text-green-700 mb-2"><?php echo $cleanup_results['summary']; ?></p>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600"><?php echo $cleanup_results['events']['count']; ?></div>
                        <div class="text-sm text-green-700">فعاليات</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600"><?php echo $cleanup_results['trips']['total']; ?></div>
                        <div class="text-sm text-green-700">رحلات</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600"><?php echo $cleanup_results['tickets']; ?></div>
                        <div class="text-sm text-green-700">تذاكر</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600"><?php echo $cleanup_results['transport_bookings']['total']; ?></div>
                        <div class="text-sm text-green-700">حجوزات مواصلات</div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <h2 class="text-lg font-semibold text-red-800 mb-2">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    خطأ في التنظيف
                </h2>
                <p class="text-red-700"><?php echo $cleanup_results['error']; ?></p>
            </div>
            <?php endif; ?>
            
            <!-- روابط للاختبار -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <h2 class="text-lg font-semibold text-yellow-800 mb-2">
                    <i class="fas fa-link ml-2"></i>
                    اختبار النتائج
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="index.php" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-center">
                        <i class="fas fa-home ml-2"></i>
                        الصفحة الرئيسية
                    </a>
                    <a href="events.php" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-center">
                        <i class="fas fa-calendar ml-2"></i>
                        صفحة الفعاليات
                    </a>
                    <a href="my-tickets.php" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-center">
                        <i class="fas fa-ticket-alt ml-2"></i>
                        تذاكري
                    </a>
                    <a href="transport/dashboard.php" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-center">
                        <i class="fas fa-tachometer-alt ml-2"></i>
                        لوحة تحكم المواصلات
                    </a>
                </div>
            </div>
            
            <!-- تعليمات الاختبار -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h2 class="text-lg font-semibold text-gray-800 mb-2">
                    <i class="fas fa-info-circle ml-2"></i>
                    تعليمات الاختبار
                </h2>
                <ol class="list-decimal list-inside text-gray-700 space-y-1">
                    <li>تحقق من الصفحة الرئيسية - يجب ألا تظهر الفعالية المنتهية</li>
                    <li>تحقق من صفحة الفعاليات - يجب ألا تظهر الفعالية المنتهية</li>
                    <li>تحقق من صفحة "تذاكري" - يجب أن تظهر التذكرة في قسم "التذاكر المنتهية"</li>
                    <li>تحقق من قسم حجوزات المواصلات في "تذاكري" - يجب أن تظهر الحجوزات المنتهية</li>
                    <li>تحقق من لوحة تحكم المواصلات - يجب أن تظهر الرحلات كغير نشطة</li>
                </ol>
            </div>
            
            <div class="mt-6 text-center">
                <a href="cleanup_expired_items.php?manual_run=1" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg" target="_blank">
                    <i class="fas fa-broom ml-2"></i>
                    تشغيل التنظيف مرة أخرى
                </a>
            </div>
        </div>
    </div>
</body>
</html>
