# نظام تعديل الملف الشخصي للإدمن

## الوصف
تم إضافة نظام شامل لتعديل الملف الشخصي للإدمن في لوحة تحكم المواصلات، يتيح للإدمن تعديل:
- الاسم
- البريد الإلكتروني  
- صورة الملف الشخصي

## الملفات المضافة/المحدثة

### 1. transport/dashboard.php (محدث)
- إضافة نافذة منبثقة لتعديل الملف الشخصي
- إضافة زر "تعديل الملف الشخصي" في القائمة المنسدلة
- تحديث عرض الصورة والاسم ليتم جلبهما من قاعدة البيانات
- إضافة JavaScript للتعامل مع النافذة المنبثقة ومعاينة الصور

### 2. transport/actions/profile_actions.php (جديد)
- معالجة طلبات تحديث الملف الشخصي
- رفع ومعالجة الصور
- التحقق من صحة البيانات
- إنشاء جدول admin_profile تلقائياً

### 3. transport/uploads/profile/ (مجلد جديد)
- مجلد لحفظ صور الملف الشخصي

## قاعدة البيانات

### جدول admin_profile
```sql
CREATE TABLE admin_profile (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL DEFAULT 1,
    name VARCHAR(255) NOT NULL DEFAULT 'المدير',
    email VARCHAR(255) NOT NULL DEFAULT '<EMAIL>',
    profile_image VARCHAR(500) DEFAULT 'assets/12.jpg',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_admin (admin_id)
);
```

## كيفية الاستخدام

### 1. الوصول للنظام
1. ادخل إلى لوحة تحكم المواصلات: `transport/dashboard.php`
2. انقر على صورة المدير في الزاوية العلوية اليمنى
3. اختر "تعديل الملف الشخصي" من القائمة المنسدلة

### 2. تعديل البيانات
1. **تغيير الصورة**: انقر على أيقونة الكاميرا لاختيار صورة جديدة
2. **تعديل الاسم**: غير النص في حقل الاسم
3. **تعديل البريد**: غير البريد الإلكتروني
4. انقر "حفظ التغييرات"

### 3. قيود النظام
- **الصور المدعومة**: JPG, PNG, GIF
- **حجم الصورة**: حد أقصى 5 ميجابايت
- **الاسم**: مطلوب ولا يمكن أن يكون فارغاً
- **البريد**: يجب أن يكون بصيغة صحيحة

## الميزات

### 1. واجهة المستخدم
- ✅ نافذة منبثقة أنيقة مع تأثيرات حركية
- ✅ معاينة فورية للصورة المختارة
- ✅ تحقق من صحة البيانات في الوقت الفعلي
- ✅ رسائل تأكيد وخطأ واضحة
- ✅ إمكانية الإغلاق بالنقر خارج النافذة

### 2. الأمان
- ✅ التحقق من نوع وحجم الملفات
- ✅ تنظيف البيانات المدخلة
- ✅ حماية من رفع ملفات ضارة
- ✅ أسماء ملفات فريدة لمنع التضارب

### 3. قاعدة البيانات
- ✅ إنشاء الجدول تلقائياً عند الحاجة
- ✅ قيم افتراضية للبيانات
- ✅ تحديث تلقائي لتاريخ التعديل

## استكشاف الأخطاء

### 1. مشاكل رفع الصور
- تأكد من وجود مجلد `transport/uploads/profile/`
- تأكد من صلاحيات الكتابة على المجلد
- تحقق من حجم ونوع الصورة

### 2. مشاكل قاعدة البيانات
- تأكد من اتصال قاعدة البيانات
- تأكد من صلاحيات إنشاء الجداول
- شغل `transport/test_profile_system.php` للتحقق

### 3. مشاكل JavaScript
- تأكد من تفعيل JavaScript في المتصفح
- تحقق من وحدة تحكم المطور للأخطاء
- تأكد من تحميل جميع ملفات CSS و JS

## التطوير المستقبلي

### إضافات مقترحة:
- [ ] إضافة إمكانية تغيير كلمة المرور
- [ ] إضافة المزيد من الحقول (الهاتف، العنوان)
- [ ] إضافة نظام صلاحيات متعدد المستويات
- [ ] إضافة سجل تغييرات الملف الشخصي
- [ ] إضافة إمكانية استعادة الصورة الافتراضية

## الدعم
في حالة وجود مشاكل أو أسئلة، يرجى مراجعة:
1. ملف `transport/test_profile_system.php` للتحقق من النظام
2. سجلات الأخطاء في PHP
3. وحدة تحكم المطور في المتصفح
