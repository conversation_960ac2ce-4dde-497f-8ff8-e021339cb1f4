# دليل البدء السريع - نظام إدارة انتهاء الصلاحية

## 🚀 البدء السريع

### 1. تحديث قاعدة البيانات
```
http://yoursite.com/update_database_schema.php
```

### 2. اختبار النظام
```
http://yoursite.com/test_expiry_system.php
```

### 3. تشغيل التنظيف يدوياً
```
http://yoursite.com/cleanup_expired_items.php?manual_run=1
```

### 4. إعداد التنظيف التلقائي
```bash
# إضافة إلى crontab (كل ساعة)
0 * * * * /usr/bin/php /path/to/your/project/cleanup_expired_items.php
```

## ✅ ما يفعله النظام

### الفعاليات المنتهية:
- ✅ تختفي من الصفحة الرئيسية
- ✅ تختفي من صفحة الفعاليات
- ✅ تحديث حالتها إلى 'expired'

### التذاكر المنتهية:
- ✅ تظهر في قسم منفصل في "تذاكري"
- ✅ تحديث حالتها إلى 'expired'
- ✅ تمييزها بلون أحمر

### الرحلات المنتهية:
- ✅ تصبح غير نشطة تلقائياً
- ✅ تختفي من خيارات الحجز الجديدة

### حجوزات المواصلات المنتهية:
- ✅ تظهر في قسم منفصل في "تذاكري"
- ✅ تحديث حالتها إلى 'expired'

## 🔧 إعدادات سريعة

### Windows (XAMPP):
```bash
# في Task Scheduler
Program: C:\xampp\php\php.exe
Arguments: C:\xampp\htdocs\new1\cleanup_expired_items.php
```

### Linux/Unix:
```bash
# إضافة إلى crontab
crontab -e
# ثم إضافة:
0 * * * * /usr/bin/php /path/to/project/cleanup_expired_items.php
```

### cPanel:
```
Command: /usr/bin/php /home/<USER>/public_html/cleanup_expired_items.php
Timing: Every Hour
```

## 📊 مراقبة النظام

### ملفات السجل:
```bash
# مراقبة السجل الحالي
tail -f logs/cleanup_$(date +%Y-%m-%d).log

# عرض آخر 20 سطر
tail -20 logs/cleanup_$(date +%Y-%m-%d).log
```

### قاعدة البيانات:
```sql
-- عرض الفعاليات المنتهية
SELECT * FROM events WHERE status = 'expired';

-- عرض التذاكر المنتهية
SELECT * FROM tickets WHERE status = 'expired';

-- عرض حجوزات المواصلات المنتهية
SELECT * FROM transport_bookings WHERE status = 'expired';
```

## 🎯 اختبار سريع

### 1. إنشاء بيانات تجريبية:
```
http://yoursite.com/test_expiry_system.php
```

### 2. التحقق من النتائج:
- الصفحة الرئيسية: لا تظهر فعاليات منتهية
- صفحة الفعاليات: لا تظهر فعاليات منتهية  
- صفحة تذاكري: تظهر التذاكر المنتهية في قسم منفصل

## 🚨 استكشاف الأخطاء

### المشكلة: Cron job لا يعمل
```bash
# تحقق من مسار PHP
which php

# تحقق من صلاحيات الملف
chmod +x cleanup_expired_items.php

# اختبار يدوي
php cleanup_expired_items.php
```

### المشكلة: لا تظهر التذاكر المنتهية
```bash
# تشغيل التنظيف يدوياً
http://yoursite.com/cleanup_expired_items.php?manual_run=1

# التحقق من قاعدة البيانات
SELECT * FROM events WHERE date_time < NOW() AND status = 'active';
```

### المشكلة: خطأ في قاعدة البيانات
```bash
# تشغيل تحديث قاعدة البيانات
http://yoursite.com/update_database_schema.php
```

## 📁 الملفات المهمة

```
cleanup_expired_items.php      # ملف التنظيف الرئيسي
update_database_schema.php     # تحديث قاعدة البيانات
test_expiry_system.php         # اختبار النظام
setup_cron.txt                 # تعليمات cron job
logs/cleanup_*.log             # ملفات السجل
```

## 🔒 الأمان

### حماية ملف التنظيف:
- يتطلب تسجيل دخول المدير للتشغيل اليدوي
- يعمل فقط من سطر الأوامر أو بمعامل خاص
- تسجيل جميع العمليات

### نصائح أمنية:
```php
// إضافة IP whitelist في cleanup_expired_items.php
$allowed_ips = ['127.0.0.1', 'your-server-ip'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips)) {
    die('Access denied');
}
```

## 📞 الدعم

### في حالة المشاكل:
1. تحقق من ملفات السجل أولاً
2. جرب التشغيل اليدوي
3. تحقق من قاعدة البيانات
4. راجع هذا الدليل

### معلومات مفيدة للدعم:
- إصدار PHP: `php -v`
- مسار PHP: `which php`
- صلاحيات الملفات: `ls -la cleanup_expired_items.php`
- آخر سجل: `tail -20 logs/cleanup_$(date +%Y-%m-%d).log`

## ✨ ميزات إضافية

### تشغيل من لوحة تحكم المواصلات:
- زر "تنظيف يدوي" في شريط التنقل
- إشعارات للمدراء عند التنظيف

### مراقبة متقدمة:
```bash
# إعداد تنبيه عند فشل التنظيف
0 * * * * /usr/bin/php /path/to/project/cleanup_expired_items.php || echo "Cleanup failed" | mail -s "Cleanup Error" <EMAIL>
```

---

**💡 نصيحة:** ابدأ بتشغيل `test_expiry_system.php` لإنشاء بيانات تجريبية واختبار النظام قبل الإعداد النهائي.
