<?php
/**
 * ملف اختبار نظام الملف الشخصي
 */

require_once __DIR__ . '/../includes/init.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();

echo "🧪 اختبار نظام الملف الشخصي للإدمن...\n\n";

try {
    // إنشاء جدول admin_profile إذا لم يكن موجوداً
    echo "📊 إنشاء جدول admin_profile...\n";
    $db->query("
        CREATE TABLE IF NOT EXISTS admin_profile (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL DEFAULT 1,
            name VARCHAR(255) NOT NULL DEFAULT 'المدير',
            email VARCHAR(255) NOT NULL DEFAULT '<EMAIL>',
            profile_image VARCHAR(500) DEFAULT 'assets/12.jpg',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_admin (admin_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $db->execute();
    echo "✅ تم إنشاء الجدول بنجاح\n\n";
    
    // التحقق من وجود سجل للإدمن
    echo "🔍 التحقق من وجود ملف شخصي للإدمن...\n";
    $db->query("SELECT * FROM admin_profile WHERE admin_id = 1");
    $profile = $db->single();
    
    if (!$profile) {
        echo "➕ إنشاء ملف شخصي افتراضي...\n";
        $db->query("
            INSERT INTO admin_profile (admin_id, name, email, profile_image, created_at, updated_at) 
            VALUES (1, 'المدير', '<EMAIL>', 'assets/12.jpg', NOW(), NOW())
        ");
        $db->execute();
        echo "✅ تم إنشاء الملف الشخصي الافتراضي\n";
    } else {
        echo "✅ الملف الشخصي موجود:\n";
        echo "   الاسم: {$profile['name']}\n";
        echo "   البريد: {$profile['email']}\n";
        echo "   الصورة: {$profile['profile_image']}\n";
    }
    
    echo "\n🎯 النظام جاهز للاستخدام!\n";
    echo "📝 يمكنك الآن:\n";
    echo "   1. الدخول إلى لوحة التحكم: transport/dashboard.php\n";
    echo "   2. النقر على صورة المدير في الأعلى\n";
    echo "   3. اختيار 'تعديل الملف الشخصي'\n";
    echo "   4. تغيير الاسم والصورة والبريد الإلكتروني\n\n";
    
    echo "🔧 الملفات المطلوبة:\n";
    echo "   ✅ transport/dashboard.php (محدث)\n";
    echo "   ✅ transport/actions/profile_actions.php (جديد)\n";
    echo "   ✅ transport/uploads/profile/ (مجلد الصور)\n\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}

echo "⏰ انتهى الاختبار في: " . date('Y-m-d H:i:s') . "\n";
?>
